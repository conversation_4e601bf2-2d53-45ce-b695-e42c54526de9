'use client'

/**
 * ComponentList - Organism component for displaying paginated component lists
 * Supports different view modes (grid, list, table) and handles pagination
 */

import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight, Grid, List, MoreHorizontal, Settings, Table } from 'lucide-react'
import { useState } from 'react'
import type { ComponentPaginatedResponse, ComponentRead } from '../types'
import { ComponentCard, ComponentCardSkeleton } from './ComponentCard'

export interface ComponentListProps {
  data?: ComponentPaginatedResponse
  isLoading?: boolean
  error?: Error | null
  viewMode?: 'grid' | 'list' | 'table'
  selectedComponents?: number[]
  showActions?: boolean
  onViewModeChange?: (mode: 'grid' | 'list' | 'table') => void
  onPageChange?: (page: number) => void
  onPageSizeChange?: (size: number) => void
  onComponentSelect?: (component: ComponentRead) => void
  onComponentEdit?: (component: ComponentRead) => void
  onComponentDelete?: (component: ComponentRead) => void
  onComponentView?: (component: ComponentRead) => void
  onTogglePreferred?: (component: ComponentRead) => void
  onSelectionChange?: (selectedIds: number[]) => void
  onBulkOperationsOpen?: () => void
  onRetry?: () => void
}

export function ComponentList({
  data,
  isLoading = false,
  error,
  viewMode = 'grid',
  selectedComponents = [],
  showActions = true,
  onViewModeChange,
  onPageChange,
  onPageSizeChange,
  onComponentSelect,
  onComponentEdit,
  onComponentDelete,
  onComponentView,
  onTogglePreferred,
  onSelectionChange,
  onBulkOperationsOpen,
  onRetry,
}: ComponentListProps) {
  const [selectAll, setSelectAll] = useState(false)

  // Handle select all toggle
  const handleSelectAll = () => {
    if (!data?.items) return

    const newSelectAll = !selectAll
    setSelectAll(newSelectAll)

    if (newSelectAll) {
      const allIds = data.items.map((component) => component.id)
      onSelectionChange?.(allIds)
    } else {
      onSelectionChange?.([])
    }
  }

  // Handle individual component selection
  const handleComponentSelection = (component: ComponentRead) => {
    const isSelected = selectedComponents.includes(component.id)
    let newSelection: number[]

    if (isSelected) {
      newSelection = selectedComponents.filter((id) => id !== component.id)
    } else {
      newSelection = [...selectedComponents, component.id]
    }

    onSelectionChange?.(newSelection)
    setSelectAll(newSelection.length === data?.items?.length)
  }

  // Handle component select (for individual selection without multi-select)
  const handleComponentSelect = (component: ComponentRead) => {
    if (onSelectionChange) {
      handleComponentSelection(component)
    }
    onComponentSelect?.(component)
  }

  // Render loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="h-8 w-48 animate-pulse rounded bg-gray-200" />
          <div className="flex gap-2">
            <div className="h-8 w-8 animate-pulse rounded bg-gray-200" />
            <div className="h-8 w-8 animate-pulse rounded bg-gray-200" />
            <div className="h-8 w-8 animate-pulse rounded bg-gray-200" />
          </div>
        </div>

        <div className="py-8 text-center">
          <p className="text-gray-600">Loading components...</p>
        </div>

        <div
          className={`grid gap-4 ${
            viewMode === 'grid'
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              : 'grid-cols-1'
          }`}
        >
          {Array.from({ length: 8 }).map((_, index) => (
            <ComponentCardSkeleton key={index} compact={viewMode === 'list'} />
          ))}
        </div>
      </div>
    )
  }

  // Render error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="mb-4 text-red-500">
          <svg className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
        <h3 className="mb-2 text-lg font-medium text-gray-900">Error loading components</h3>
        <p className="max-w-md text-center text-gray-600" data-testid="error-message">
          {error.message || 'An unexpected error occurred while loading the components.'}
        </p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => onRetry?.() || window.location.reload()}
          data-testid="retry-btn"
        >
          Try Again
        </Button>
      </div>
    )
  }

  // Render empty state
  if (!data?.items || data.items.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="mb-4 text-gray-400">
          <svg className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
            />
          </svg>
        </div>
        <h3 className="mb-2 text-lg font-medium text-gray-900">No components found</h3>
        <p className="max-w-md text-center text-gray-600">
          No components match your current search criteria. Try adjusting your filters or search
          terms.
        </p>
      </div>
    )
  }

  const { items, page, size, total, pages } = data

  return (
    <div className="space-y-4" role="region" aria-label="component list" data-testid="component-list">
      {/* Header with view controls and pagination info */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {onSelectionChange && (
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={selectedComponents.length === components.length && components.length > 0}
                onChange={(e) => {
                  if (e.target.checked) {
                    onSelectionChange(components.map(c => c.id))
                  } else {
                    onSelectionChange([])
                  }
                }}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                data-testid="select-all-components"
                aria-label="Select all components"
              />
              <span className="text-sm text-gray-600">Select All</span>
            </div>
          )}

          <p className="text-sm text-gray-600">
            Showing {(page - 1) * size + 1} to {Math.min(page * size, total)} of {total} components
          </p>

          {selectedComponents.length > 0 && (
            <>
              <p className="text-sm font-medium text-blue-600" data-testid="selection-count">
                {selectedComponents.length} selected
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkOperationsOpen?.()}
                className="flex items-center gap-2"
                data-testid="bulk-operations-btn"
              >
                <Settings className="h-4 w-4" />
                Bulk Operations
              </Button>
            </>
          )}
        </div>

        {/* View mode controls */}
        <div className="flex items-center gap-1 rounded-lg border p-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onViewModeChange?.('grid')}
            className={`h-8 w-8 p-0 ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : ''}`}
            aria-label="Grid view"
            data-testid="grid-view-btn"
          >
            <Grid className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onViewModeChange?.('list')}
            className={`h-8 w-8 p-0 ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : ''}`}
            aria-label="List view"
            data-testid="list-view-btn"
          >
            <List className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onViewModeChange?.('table')}
            className={`h-8 w-8 p-0 ${viewMode === 'table' ? 'bg-blue-100 text-blue-600' : ''}`}
            aria-label="Table view"
            data-testid="table-view-btn"
          >
            <Table className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Bulk selection controls */}
      {onSelectionChange && (
        <div className="flex items-center gap-2 border-b py-2">
          <input
            type="checkbox"
            checked={selectAll}
            onChange={handleSelectAll}
            className="rounded border-gray-300"
            aria-label="Select all components"
            data-testid="select-all-components"
          />
          <span className="text-sm text-gray-600">
            Select all {items.length} components on this page
          </span>
        </div>
      )}

      {/* Component grid/list */}
      <div
        className={`grid gap-4 ${
          viewMode === 'grid'
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
            : 'grid-cols-1'
        }`}
        data-testid="component-grid"
      >
        {items.map((component) => (
          <ComponentCard
            key={component.id}
            component={component}
            isSelected={selectedComponents.includes(component.id)}
            compact={viewMode === 'list'}
            showActions={showActions}
            onSelect={handleComponentSelect}
            onEdit={onComponentEdit}
            onDelete={onComponentDelete}
            onView={onComponentView}
            onTogglePreferred={onTogglePreferred}
          />
        ))}
      </div>

      {/* Pagination */}
      {pages > 1 && (
        <div className="flex items-center justify-between pt-4">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Rows per page:</span>
            <select
              value={size}
              onChange={(e) => onPageSizeChange?.(Number(e.target.value))}
              className="rounded border px-2 py-1 text-sm"
              aria-label="Items per page"
              data-testid="page-size-select"
            >
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">
              Page {page} of {pages}
            </span>

            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(page - 1)}
              disabled={page <= 1}
              aria-label="Previous page"
              data-testid="prev-page-btn"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>

            <div className="flex items-center gap-1">
              {/* Page numbers */}
              {Array.from({ length: Math.min(5, pages) }, (_, i) => {
                const pageNum = Math.max(1, page - 2) + i
                if (pageNum > pages) return null

                return (
                  <Button
                    key={pageNum}
                    variant={pageNum === page ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => onPageChange?.(pageNum)}
                    className="h-8 w-8 p-0"
                  >
                    {pageNum}
                  </Button>
                )
              })}

              {pages > 5 && page < pages - 2 && (
                <>
                  <MoreHorizontal className="h-4 w-4 text-gray-400" />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange?.(pages)}
                    className="h-8 w-8 p-0"
                  >
                    {pages}
                  </Button>
                </>
              )}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(page + 1)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  onPageChange?.(page + 1)
                }
              }}
              disabled={page >= pages}
              aria-label="Next page"
              data-testid="next-page-btn"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
