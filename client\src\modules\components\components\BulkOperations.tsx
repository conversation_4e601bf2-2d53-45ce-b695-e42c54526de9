'use client'

/**
 * BulkOperations - Component for handling bulk operations on components
 * Supports bulk create, update, delete, and export operations
 */

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
    Alert<PERSON>riangle,
    CheckCircle,
    Download,
    Edit,
    FileText,
    Loader2,
    Trash2,
    Upload,
    XCircle,
} from 'lucide-react'
import { useState } from 'react'
import {
    useBulkCreateComponents,
    useBulkDeleteComponents,
    useBulkUpdateComponents,
} from '../api/componentMutations'

export interface BulkOperationsProps {
  selectedComponentIds: number[]
  onSelectionClear?: () => void
  onBulkEdit?: (ids: number[]) => void
  onOperationComplete?: () => void
  className?: string
}

export function BulkOperations({
  selectedComponentIds,
  onSelectionClear,
  onOperationComplete,
  className = '',
}: BulkOperationsProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState<string | null>(null)
  const [operationResults, setOperationResults] = useState<any[]>([])

  // Mutations
  const bulkCreateMutation = useBulkCreateComponents()
  const bulkUpdateMutation = useBulkUpdateComponents()
  const bulkDeleteMutation = useBulkDeleteComponents()

  const selectedCount = selectedComponentIds.length
  const hasSelection = selectedCount > 0

  // Handle bulk delete
  const handleBulkDelete = async () => {
    try {
      const result = await bulkDeleteMutation.mutateAsync({
        componentIds: selectedComponentIds,
        soft_delete: false,
      })

      setOperationResults([result])
      onSelectionClear?.()
      onOperationComplete?.()
      setShowConfirmDialog(null)
    } catch (error) {
      console.error('Bulk delete failed:', error)
    }
  }

  // Handle bulk update (example: mark as preferred)
  const handleBulkUpdate = async (updateData: Partial<any>) => {
    try {
      const result = await bulkUpdateMutation.mutateAsync({
        component_ids: selectedComponentIds,
        update_data: updateData,
      })

      setOperationResults(result)
      onSelectionClear?.()
      onOperationComplete?.()
    } catch (error) {
      console.error('Bulk update failed:', error)
    }
  }

  // Handle export
  const handleExport = () => {
    // This would typically trigger a download
    const exportData = {
      component_ids: selectedComponentIds,
      format: 'csv',
      timestamp: new Date().toISOString(),
    }

    console.log('Exporting components:', exportData)
    // In a real implementation, this would call an export API
  }

  const ConfirmDialog = ({
    title,
    message,
    onConfirm,
    onCancel,
    isDestructive = false,
  }: {
    title: string
    message: string
    onConfirm: () => void
    onCancel: () => void
    isDestructive?: boolean
  }) => (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle
              className={`h-5 w-5 ${isDestructive ? 'text-red-500' : 'text-yellow-500'}`}
            />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="mb-6 text-gray-700">{message}</p>
          <div className="flex items-center justify-end gap-3">
            <Button variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button variant={isDestructive ? 'destructive' : 'default'} onClick={onConfirm} data-testid="confirm-bulk-delete">
              Confirm
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  return (
    <div className={className}>
      {/* Selection Summary */}
      {hasSelection && (
        <Card className="mb-4">
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-blue-500" />
                <span className="font-medium" data-testid="selection-count">
                  {selectedCount} component{selectedCount !== 1 ? 's' : ''} selected
                </span>
              </div>

              <Button variant="ghost" size="sm" onClick={onSelectionClear} data-testid="clear-selection-btn">
                Clear Selection
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bulk Operations */}
      <Card data-testid="bulk-operations-panel">
        <CardHeader>
          <CardTitle>Bulk Operations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            {/* Bulk Edit */}
            <Button
              variant="outline"
              disabled={!hasSelection}
              onClick={() => onBulkEdit?.(selectedIds)}
              className="flex items-center gap-2"
              data-testid="bulk-edit-btn"
            >
              <Edit className="h-4 w-4" />
              Bulk Edit
            </Button>

            {/* Bulk Update Operations */}
            <Button
              variant="outline"
              disabled={!hasSelection || bulkUpdateMutation.isPending}
              onClick={() => handleBulkUpdate({ is_preferred: true })}
              className="flex items-center gap-2"
            >
              {bulkUpdateMutation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Edit className="h-4 w-4" />
              )}
              Mark as Preferred
            </Button>

            <Button
              variant="outline"
              disabled={!hasSelection || bulkUpdateMutation.isPending}
              onClick={() => handleBulkUpdate({ is_active: false })}
              className="flex items-center gap-2"
            >
              {bulkUpdateMutation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Edit className="h-4 w-4" />
              )}
              Deactivate
            </Button>

            {/* Export */}
            <Button
              variant="outline"
              disabled={!hasSelection}
              onClick={handleExport}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Export Selected
            </Button>

            {/* Bulk Delete */}
            <Button
              variant="outline"
              disabled={!hasSelection || bulkDeleteMutation.isPending}
              onClick={() => setShowConfirmDialog('delete')}
              className="flex items-center gap-2 text-red-600 hover:text-red-700"
              data-testid="bulk-delete-btn"
            >
              {bulkDeleteMutation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="h-4 w-4" />
              )}
              Delete Selected
            </Button>
          </div>

          {/* Import Section */}
          <div className="mt-6 border-t pt-6">
            <h4 className="mb-3 font-medium text-gray-900">Import Components</h4>
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                className="flex items-center gap-2"
                onClick={() => {
                  // This would open a file picker
                  console.log('Opening file picker for import')
                }}
              >
                <Upload className="h-4 w-4" />
                Import from CSV
              </Button>

              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-2"
                onClick={() => {
                  // This would download a template
                  console.log('Downloading CSV template')
                }}
              >
                <FileText className="h-4 w-4" />
                Download Template
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Operation Results */}
      {operationResults.length > 0 && (
        <Card className="mt-4">
          <CardHeader>
            <CardTitle>Operation Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {operationResults.map((result, index) => (
                <div key={index} className="flex items-center gap-2 text-sm">
                  {result.is_valid ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <span>
                    {result.is_valid
                      ? `Successfully processed component ${result.component?.name || 'Unknown'}`
                      : `Failed to process: ${result.errors?.join(', ') || 'Unknown error'}`}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Confirmation Dialogs */}
      {showConfirmDialog === 'delete' && (
        <ConfirmDialog
          title="Confirm Bulk Delete"
          message={`Are you sure you want to delete ${selectedCount} component${selectedCount !== 1 ? 's' : ''}? This action cannot be undone.`}
          onConfirm={handleBulkDelete}
          onCancel={() => setShowConfirmDialog(null)}
          isDestructive={true}
        />
      )}
    </div>
  )
}
