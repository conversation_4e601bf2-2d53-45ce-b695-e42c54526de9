# Page snapshot

```yaml
- main:
  - button "Back"
  - heading "Create New Component" [level=1]
  - paragraph: Add a new electrical component to the catalog
  - heading "Create New Component" [level=3]
  - form:
    - text: Component Name *
    - textbox "Component Name *": Partial Component
    - text: Manufacturer *
    - textbox "Manufacturer *"
    - text: Model Number *
    - textbox "Model Number *"
    - text: Part Number *
    - textbox "Part Number *"
    - text: Category
    - combobox "Category":
      - option "Select category" [selected]
    - text: Component Type
    - combobox "Component Type":
      - option "Select component type" [selected]
    - text: Description
    - textbox "Description"
    - text: Unit Price
    - spinbutton "Unit Price"
    - text: Currency
    - combobox "Currency":
      - option "USD"
      - option "EUR" [selected]
      - option "GBP"
      - option "CAD"
    - text: Weight (kg)
    - spinbutton "Weight (kg)"
    - checkbox "Active" [checked]
    - text: Active
    - checkbox "Preferred"
    - text: Preferred
    - button "Cancel"
    - button "Create"
- button "Open Tanstack query devtools":
  - img
- alert
```