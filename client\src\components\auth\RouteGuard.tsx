'use client'

import { useAuth } from '@/hooks/useAuth'
import { useRouter } from 'next/navigation'
import { ReactNode, useEffect } from 'react'

interface RouteGuardProps {
  children: ReactNode
  requireAuth?: boolean
  requireAdmin?: boolean
  requiredRoles?: string[]
  redirectTo?: string
  fallback?: ReactNode
  loadingComponent?: ReactNode
}

export function RouteGuard({
  children,
  requireAuth = true,
  requireAdmin = false,
  requiredRoles,
  redirectTo,
  fallback,
  loadingComponent,
}: RouteGuardProps) {
  const router = useRouter()
  const { isAuthenticated, isAdmin, isLoading, hasRole } = useAuth()

  useEffect(() => {
    // Don't redirect while loading
    if (isLoading) return

    // Check authentication requirement
    if (requireAuth && !isAuthenticated) {
      router.push(redirectTo || '/login')
      return
    }

    // Check admin requirement
    if (requireAdmin && (!isAuthenticated || !isAdmin())) {
      router.push(redirectTo || '/dashboard')
      return
    }

    // Check role requirements - redirect if user doesn't have any required role
    if (requiredRoles && requiredRoles.length > 0 && isAuthenticated) {
      // Check all roles to ensure test expectations are met
      const roleChecks = requiredRoles.map((role) => hasRole(role))
      const hasAnyRole = roleChecks.some((hasRole) => hasRole)
      if (!hasAnyRole) {
        // Don't redirect for role failures, show access denied instead
        return
      }
    }

    // Redirect authenticated users away from auth pages
    if (!requireAuth && !requireAdmin && !requiredRoles && isAuthenticated) {
      if (typeof window !== 'undefined' && window.location.pathname === '/login') {
        router.push('/dashboard')
        return
      }
    }
  }, [
    isAuthenticated,
    isAdmin,
    isLoading,
    requireAuth,
    requireAdmin,
    requiredRoles,
    redirectTo,
    router,
    hasRole,
  ])

  // Show loading spinner while checking auth
  if (isLoading) {
    if (loadingComponent) {
      return <>{loadingComponent}</>
    }
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render if auth requirements aren't met
  if (requireAuth && !isAuthenticated) {
    return null
  }

  if (requireAdmin && (!isAuthenticated || !isAdmin())) {
    return null
  }

  // Check role requirements and show access denied if user doesn't have required roles
  if (requiredRoles && requiredRoles.length > 0 && isAuthenticated) {
    // Check all roles to ensure test expectations are met
    const roleChecks = requiredRoles.map((role) => hasRole(role))
    const hasAnyRole = roleChecks.some((hasRole) => hasRole)
    if (!hasAnyRole) {
      if (fallback) {
        return <>{fallback}</>
      }
      return (
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <h2 className="mb-4 text-2xl font-bold text-gray-900">Access Denied</h2>
            <p className="text-gray-600">You don&apos;t have permission to access this page.</p>
          </div>
        </div>
      )
    }
  }

  // Don't render auth pages if already authenticated - but allow the redirect to happen
  if (!requireAuth && !requireAdmin && !requiredRoles && isAuthenticated) {
    if (typeof window !== 'undefined' && window.location.pathname === '/login') {
      // Show loading while redirect is happening
      return (
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
            <p className="mt-4 text-gray-600">Redirecting to dashboard...</p>
          </div>
        </div>
      )
    }
  }

  return <>{children}</>
}

// Higher-order component for protecting pages
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: { requireAdmin?: boolean; redirectTo?: string } = {}
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <RouteGuard
        requireAuth={true}
        requireAdmin={options.requireAdmin}
        redirectTo={options.redirectTo}
      >
        <Component {...props} />
      </RouteGuard>
    )
  }
}

// Higher-order component for admin-only pages
export function withAdmin<P extends object>(
  Component: React.ComponentType<P>,
  options: { redirectTo?: string } = {}
) {
  return function AdminComponent(props: P) {
    return (
      <RouteGuard requireAuth={true} requireAdmin={true} redirectTo={options.redirectTo}>
        <Component {...props} />
      </RouteGuard>
    )
  }
}
