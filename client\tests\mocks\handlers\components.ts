/**
 * MSW handlers for component API endpoints
 */

import { http, HttpResponse } from 'msw'
import { AuthErrors, getUserFromRequest } from '../fixtures/auth'
import {
    createMockComponent,
    filterComponents,
    mockComponentCategories,
    mockComponents,
    mockComponentSuggestions,
    mockComponentTypes
} from '../fixtures/components'

const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'
console.log('🔧 MSW: API_BASE configured as:', API_BASE)

// In-memory storage for components during testing
let components = [...mockComponents]
let nextId = 6

export const componentHandlers = [
  // Debug: Log all requests to see what's being called
  http.all('*', ({ request }) => {
    if (request.url.includes('/api/v1/component')) {
      console.log('🌐 MSW: API request detected:', request.method, request.url)
    }
    return
  }),

  // GET /api/v1/components/ - List components with filtering
  http.get(`${API_BASE}/api/v1/components/`, ({ request }) => {
    console.log('🔍 MSW: Component list request intercepted!', request.url)

    // Check authentication for protected endpoint
    const user = getUserFromRequest(request)
    if (!user) {
      console.log('❌ MSW: Authentication failed for component list')
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }

    console.log('✅ MSW: Authentication passed for component list, user:', user.email)

    const url = new URL(request.url)
    const searchParams = Object.fromEntries(url.searchParams)

    // Apply filters
    const filteredComponents = filterComponents({
      search_term: searchParams.search || searchParams.search_term,
      category: searchParams.category,
      manufacturer: searchParams.manufacturer,
      is_preferred: searchParams.is_preferred === 'true' ? true : searchParams.is_preferred === 'false' ? false : undefined,
      is_active: searchParams.is_active === 'true' ? true : searchParams.is_active === 'false' ? false : undefined,
    })

    // Pagination
    const page = parseInt(searchParams.page, 10) || 1
    const perPage = parseInt(searchParams.size, 10) || 10
    const startIndex = (page - 1) * perPage
    const endIndex = startIndex + perPage
    const paginatedComponents = filteredComponents.slice(startIndex, endIndex)

    const response = {
      items: paginatedComponents,
      total: filteredComponents.length,
      page,
      size: perPage,
      pages: Math.ceil(filteredComponents.length / perPage),
    }

    console.log('📦 MSW: Returning component list response:', response)
    return HttpResponse.json(response)
  }),

  // GET /api/v1/components/:id - Get component by ID
  http.get(`${API_BASE}/api/v1/components/:id`, ({ params, request }) => {
    // Check authentication for protected endpoint
    const user = getUserFromRequest(request)
    if (!user) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }
    const { id } = params
    const component = components.find(c => c.id === parseInt(id as string))

    if (!component) {
      return HttpResponse.json(
        { error: 'Component not found' },
        { status: 404 }
      )
    }

    return HttpResponse.json({ data: component })
  }),

  // POST /api/v1/components/ - Create new component
  http.post(`${API_BASE}/api/v1/components/`, async ({ request }) => {
    // Check authentication for protected endpoint
    const user = getUserFromRequest(request)
    if (!user) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }

    const body = await request.json() as any

    // Basic validation
    if (!body.name || !body.manufacturer || !body.model_number) {
      return HttpResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const newComponent = createMockComponent({
      ...body,
      id: nextId++,
    })

    components.push(newComponent)

    return HttpResponse.json({ data: newComponent }, { status: 201 })
  }),

  // PUT /api/v1/components/:id - Update component
  http.put(`${API_BASE}/api/v1/components/:id`, async ({ params, request }) => {
    // Check authentication for protected endpoint
    const user = getUserFromRequest(request)
    if (!user) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }

    const { id } = params
    const body = await request.json() as any

    const componentIndex = components.findIndex(c => c.id === parseInt(id as string))
    if (componentIndex === -1) {
      return HttpResponse.json(
        { error: 'Component not found' },
        { status: 404 }
      )
    }

    const updatedComponent = {
      ...components[componentIndex],
      ...body,
      updated_at: new Date().toISOString(),
    }

    components[componentIndex] = updatedComponent

    return HttpResponse.json({ data: updatedComponent })
  }),

  // DELETE /api/v1/components/:id - Delete component
  http.delete(`${API_BASE}/api/v1/components/:id`, ({ params, request }) => {
    // Check authentication for protected endpoint
    const user = getUserFromRequest(request)
    if (!user) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }

    const { id } = params
    const componentIndex = components.findIndex(c => c.id === parseInt(id as string))

    if (componentIndex === -1) {
      return HttpResponse.json(
        { error: 'Component not found' },
        { status: 404 }
      )
    }

    components.splice(componentIndex, 1)

    return HttpResponse.json({ message: 'Component deleted successfully' })
  }),

  // GET /api/v1/component-categories - List component categories
  http.get(`${API_BASE}/api/v1/component-categories`, ({ request }) => {
    console.log('🔍 MSW: Component categories request intercepted!', request.url)

    // Check authentication for protected endpoint
    const user = getUserFromRequest(request)
    if (!user) {
      console.log('❌ MSW: Authentication failed for component categories')
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }

    console.log('✅ MSW: Returning component categories:', mockComponentCategories.length, 'items')
    return HttpResponse.json({ data: mockComponentCategories })
  }),

  // GET /api/v1/component-types - List component types
  http.get(`${API_BASE}/api/v1/component-types`, ({ request }) => {
    console.log('🔍 MSW: Component types request intercepted!', request.url)

    // Check authentication for protected endpoint
    const user = getUserFromRequest(request)
    if (!user) {
      console.log('❌ MSW: Authentication failed for component types')
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }

    const url = new URL(request.url)
    const category = url.searchParams.get('category')

    let filteredTypes = mockComponentTypes

    // Filter by category if specified
    if (category) {
      // Simple category-based filtering (in real app this would be more sophisticated)
      filteredTypes = mockComponentTypes.filter(type => {
        switch (category) {
          case 'RESISTOR':
            return type.value.includes('RESISTOR')
          case 'CAPACITOR':
            return type.value.includes('CAPACITOR')
          case 'INDUCTOR':
            return type.value.includes('INDUCTOR')
          case 'TRANSISTOR':
            return ['BJT', 'MOSFET'].includes(type.value)
          case 'DIODE':
            return type.value.includes('DIODE')
          default:
            return true
        }
      })
    }

    console.log('✅ MSW: Returning component types:', filteredTypes.length, 'items')
    return HttpResponse.json({ data: filteredTypes })
  }),

  // GET /api/v1/components/suggestions - Get component suggestions
  http.get(`${API_BASE}/api/v1/components/suggestions`, ({ request }) => {
    // Check authentication for protected endpoint
    const user = getUserFromRequest(request)
    if (!user) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }

    const url = new URL(request.url)
    const query = url.searchParams.get('q') || ''
    const limit = parseInt(url.searchParams.get('limit') || '10')

    if (query.length < 2) {
      return HttpResponse.json({ data: [] })
    }

    const suggestions = mockComponentSuggestions
      .filter(suggestion => suggestion.toLowerCase().includes(query.toLowerCase()))
      .slice(0, limit)

    return HttpResponse.json({ data: suggestions })
  }),

  // Bulk operations
  http.post(`${API_BASE}/api/v1/components/bulk-delete`, async ({ request }) => {
    // Check authentication for protected endpoint
    const user = getUserFromRequest(request)
    if (!user) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }

    const body = await request.json() as { ids: number[] }

    components = components.filter(c => !body.ids.includes(c.id))

    return HttpResponse.json({
      message: `Successfully deleted ${body.ids.length} components`
    })
  }),

  http.post(`${API_BASE}/api/v1/components/bulk-update`, async ({ request }) => {
    // Check authentication for protected endpoint
    const user = getUserFromRequest(request)
    if (!user) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }

    const body = await request.json() as {
      ids: number[]
      updates: Partial<typeof mockComponents[0]>
    }

    components = components.map(c => {
      if (body.ids.includes(c.id)) {
        return {
          ...c,
          ...body.updates,
          updated_at: new Date().toISOString(),
        }
      }
      return c
    })

    return HttpResponse.json({
      message: `Successfully updated ${body.ids.length} components`
    })
  }),
]