# Page snapshot

```yaml
- main:
  - heading "Component Catalog" [level=1]
  - paragraph: Manage electrical components and specifications
  - button "Show Stats"
  - button "Export"
  - button "Add Component"
  - heading "Filters" [level=3]
  - text: "1"
  - button "Clear all"
  - button
  - button "Category"
  - combobox:
    - option "All Categories" [selected]
  - radio "All Categories" [checked]
  - text: All Categories
  - button "Component Type"
  - radio "All Types" [checked]
  - text: All Types
  - button "Manufacturer"
  - button "Price Range"
  - button "Status"
  - text: Preferred Status
  - radio "All" [checked]
  - text: All
  - radio "Preferred Only"
  - text: Preferred Only
  - radio "Not Preferred"
  - text: Not Preferred Active Status
  - radio "All" [checked]
  - text: All
  - radio "Active Only"
  - text: Active Only
  - radio "Inactive"
  - text: Inactive
  - textbox "Search components"
  - img
  - heading "Error loading components" [level=3]
  - paragraph: NetworkError when attempting to fetch resource.
  - button "Try Again"
  - button "Hide Filters"
- button "Open Tanstack query devtools":
  - img
- alert
```