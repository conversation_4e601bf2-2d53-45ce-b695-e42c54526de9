'use client'

/**
 * ComponentDetails - Detailed view component for component information
 * Displays comprehensive component data with actions
 */

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
    Building,
    Calendar,
    DollarSign,
    Edit,
    FileText,
    Heart,
    Package,
    Ruler,
    Star,
    Tag,
    Trash2
} from 'lucide-react'
import type { ComponentRead } from '../types'
import {
    formatComponentName,
    formatDimensions,
    formatPrice,
    formatWeight,
    getComponentStatusColor,
    getComponentStatusText,
} from '../utils'

export interface ComponentDetailsProps {
  component: ComponentRead
  isLoading?: boolean
  onEdit?: (component: ComponentRead) => void
  onDelete?: (component: ComponentRead) => void
  onTogglePreferred?: (component: ComponentRead) => void
  className?: string
}

export function ComponentDetails({
  component,
  isLoading = false,
  onEdit,
  onDelete,
  onTogglePreferred,
  className = '',
}: ComponentDetailsProps) {
  const statusColor = getComponentStatusColor(component)
  const statusText = getComponentStatusText(component)

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        {Array.from({ length: 4 }).map((_, index) => (
          <Card key={index}>
            <CardHeader>
              <div className="h-6 w-48 animate-pulse rounded bg-gray-200" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="h-4 w-full animate-pulse rounded bg-gray-200" />
                <div className="h-4 w-3/4 animate-pulse rounded bg-gray-200" />
                <div className="h-4 w-1/2 animate-pulse rounded bg-gray-200" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`} data-testid="component-details">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="mb-2 text-2xl font-bold text-gray-900">
                {formatComponentName(component)}
              </CardTitle>
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <span className="flex items-center gap-1">
                  <Building className="h-4 w-4" />
                  {component.manufacturer}
                </span>
                <span className="flex items-center gap-1">
                  <Tag className="h-4 w-4" />
                  {component.model_number}
                </span>
                <Badge
                  variant={component.is_active ? 'default' : 'secondary'}
                  className={statusColor}
                >
                  {statusText}
                </Badge>
                {component.is_preferred && (
                  <Badge variant="outline" className="border-yellow-600 text-yellow-600">
                    <Star className="mr-1 h-3 w-3 fill-current" />
                    Preferred
                  </Badge>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onTogglePreferred?.(component)}
                className={component.is_preferred ? 'text-yellow-600' : ''}
              >
                <Heart className={`mr-2 h-4 w-4 ${component.is_preferred ? 'fill-current' : ''}`} />
                {component.is_preferred ? 'Remove from Preferred' : 'Add to Preferred'}
              </Button>

              <Button variant="outline" size="sm" onClick={() => onEdit?.(component)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => onDelete?.(component)}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Basic Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-500">Category</label>
              <p className="text-gray-900">{component.category || 'Not specified'}</p>
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-500">Component Type</label>
              <p className="text-gray-900">{component.component_type || 'Not specified'}</p>
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-500">Part Number</label>
              <p className="text-gray-900">{component.part_number || 'Not specified'}</p>
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-500">Supplier</label>
              <p className="text-gray-900">{component.supplier || 'Not specified'}</p>
            </div>
          </div>

          {component.description && (
            <div className="mt-6">
              <label className="mb-2 block text-sm font-medium text-gray-500">Description</label>
              <p className="leading-relaxed text-gray-900">{component.description}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pricing & Physical Properties */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Pricing
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-500">Unit Price</label>
                <p className="text-2xl font-bold text-gray-900">
                  {formatPrice(component.unit_price ?? null, component.currency)}
                </p>
              </div>

              <div>
                <label className="mb-1 block text-sm font-medium text-gray-500">Currency</label>
                <p className="text-gray-900">{component.currency}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Ruler className="h-5 w-5" />
              Physical Properties
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-500">Weight</label>
                <p className="text-gray-900">{formatWeight(component.weight_kg ?? null)}</p>
              </div>

              <div>
                <label className="mb-1 block text-sm font-medium text-gray-500">Dimensions</label>
                <p className="text-gray-900">{formatDimensions(component.dimensions)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Specifications */}
      {component.specifications && Object.keys(component.specifications).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Specifications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              {Object.entries(component.specifications).map(([key, value]) => (
                <div key={key}>
                  <label className="mb-1 block text-sm font-medium capitalize text-gray-500">
                    {key.replace(/_/g, ' ')}
                  </label>
                  <p className="text-gray-900">
                    {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Metadata */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Metadata
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-500">Created At</label>
              <p className="text-gray-900">{new Date(component.created_at).toLocaleDateString()}</p>
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-500">Updated At</label>
              <p className="text-gray-900">{new Date(component.updated_at).toLocaleDateString()}</p>
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-500">Component ID</label>
              <p className="font-mono text-gray-900">{component.id}</p>
            </div>
          </div>

          {component.metadata && Object.keys(component.metadata).length > 0 && (
            <div className="mt-6">
              <label className="mb-2 block text-sm font-medium text-gray-500">
                Additional Metadata
              </label>
              <div className="rounded-lg bg-gray-50 p-4">
                <pre className="whitespace-pre-wrap text-sm text-gray-700">
                  {JSON.stringify(component.metadata, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
